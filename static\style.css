/* style.css for static home page */
body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  background: #e3f6fd;
}

nav {
  background: #0288d1;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 1.5rem;
  flex-shrink: 0;
}

nav a {
  color: #fff;
  text-decoration: none;
  margin: 0 1.5rem;
  font-weight: bold;
  transition: color 0.2s;
}

nav a:hover {
  color: #b3e5fc;
}

.hero {
  background: url("../images/poolUnsplash.jpg") center/cover no-repeat;
  color: #fff;
  padding: 6rem 2rem;
  text-align: center;
  box-shadow: 0 4px 16px #0288d133;
  position: relative;
}

.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(2, 136, 209, 0.3);
  z-index: 1;
}

.hero h1,
.hero p {
  position: relative;
  z-index: 2;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 8px #0288d199;
}

.hero p {
  font-size: 1.5rem;
  text-shadow: 1px 1px 6px #0288d188;
}

.content {
  max-width: 900px;
  margin: 2rem auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px #0288d122;
  padding: 2rem;
}

.features {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  justify-content: space-between;
}

.feature {
  flex: 1 1 250px;
  background: #b3e5fc;
  border-radius: 6px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 1px 4px #0288d111;
}

footer {
  background: #0288d1;
  color: #fff;
  text-align: center;
  padding: 1rem 0;
  margin-top: 2rem;
}
